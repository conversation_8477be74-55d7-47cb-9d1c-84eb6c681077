import React, {useCallback, useEffect} from 'react';
import {
  StyleSheet,
  View,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  BackHandler,
} from 'react-native';
import {CCAppBar} from '../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import {StepMenuVertical, IOptionMenuItem} from '../../components/AcStepMenu';
import {FOOTER_HEIGHT} from '../../styles';
import {SubmitFlowItem, useSubmitFlow} from '../../hooks/useSubmitFlow';
import {Button, Card, IconButton, useTheme} from 'react-native-paper';
import {useCommonProcedure} from '../../stores';
import {
  useNavigation,
  NavigationProp,
  useFocusEffect,
} from '@react-navigation/native';
import {MainStackParamList} from '../../navigation/MainNavigator';
import {HomeStackParamList} from '../../navigation/HomeNavigator';
import {getPdfAsBase64} from '../document/utils';
import moment from 'moment';
import {
  createDocDto,
  document,
} from '../../requester-biz-service/apis/documents-api'; // Assuming you have a document store to handle document creation
import {useMutation, useQueryClient} from 'react-query';
import {getCitizenCode, useUserStore} from '../../stores/user.store';
import {dvcQuangNinh} from '../../requester-biz-service/apis/dvc-quang-ninh';
import {documentSet} from '../../requester-biz-service/apis/document-sets-api';
import {commonStyle} from './commonStyle';

const optionSteps: IOptionMenuItem[] = [
  {id: 1, name: 'Khai báo thông tin người yêu cầu'},
  {id: 2, name: 'Kê khai biểu mẫu điện tử'},
  {id: 3, name: 'Đính kèm giấy tờ'},
  {id: 4, name: 'Nhận kết quả & thanh toán'},
];

const getSubmitItem = (array: SubmitFlowItem[] | undefined) => {
  if (!array) {
    return [];
  }
  return (array || []).map((item: SubmitFlowItem) => ({
    code: item.code,
    name: item.name,
    uri: item.uri,
    dataType: item.dataType,
    isAttachmentUri: item.isAttachmentUri,
    value: item.value,
  }));
};

const StepThree = () => {
  // Function to get PDF file as base64
  const nav = useNavigation<NavigationProp<MainStackParamList>>();
  const navHome = useNavigation<NavigationProp<HomeStackParamList>>();

  const theme = useTheme();
  const queryClient = useQueryClient();
  const itemsArray = useSubmitFlow(state => state.itemsArray);

  const templateDetail = useCommonProcedure(state => state.templateDetail);
  const [formItem, setFormItem] = React.useState<any>(null);
  const addItemToArray = useSubmitFlow(state => state.addItemToArray);
  const clearUriAndMetaDataByCode = useSubmitFlow(
    state => state.clearUriAndMetaDataByCode,
  );
  const editItemInArrayByCodeAndUri = useSubmitFlow(
    state => state.editItemInArrayByCodeAndUri,
  );
  const displayName = useUserStore(state => state.getDisplayName());
  const stepOneFormData = useSubmitFlow(state => state.stepOneFormData);
  const metadataSubmitFlow = useSubmitFlow(state => state.metadata);
  const currentUuid = useSubmitFlow(state => state.currentUuid);

  const forwardData = useCommonProcedure(state => state.forwardData);
  const setSubmitDocumentResponse = useSubmitFlow(
    state => state.setSubmitDocumentResponse,
  );
  const setSubmitDocumentInstantResponse = useSubmitFlow(
    state => state.setSubmitDocumentInstantResponse,
  );

  const {userProfile} = useUserStore();
  useEffect(() => {
    templateDetail.data.forEach((item: any) => {
      if (item.dataType === 'uri') {
        addItemToArray({
          name: item.name,
          code: item.code,
          uri: item.uri,
          dataType: item.dataType,
          required: item.required,
        });
      }
    });
  }, [addItemToArray, templateDetail]);

  // Set formItem only when itemsArray changes and a 'form' item is found
  useEffect(() => {
    if (Array.isArray(itemsArray)) {
      const form = itemsArray.find((item: any) => item.dataType === 'form');
      if (form) {
        setFormItem(form);
      }
    }
  }, [itemsArray]);

  const handleBackPress = useCallback(() => {
    nav.goBack();
  }, [nav]);

  // Handle hardware back button (Android)
  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        handleBackPress();
        return true; // Prevent default behavior
      };

      const subscription = BackHandler.addEventListener(
        'hardwareBackPress',
        onBackPress,
      );

      return () => subscription.remove();
    }, [handleBackPress]),
  );

  const uploadMutation = useMutation({
    mutationFn: (data: createDocDto) => document.createDoc(data),
    onSuccess: async data => {
      // Log the response body
      queryClient.invalidateQueries(['documents']);

      await editItemInArrayByCodeAndUri(formItem.code, data.data.metadata.uri);

      // Invalidate and refetch documents queries
    },
    onError: (error: any) => {
      console.error('Upload error:', error);
      // Print out response body if available
      if (error?.response?.data) {
        console.error(
          'Error response data:',
          JSON.stringify(error.response.data, null, 2),
        );
      }
      if (error?.response?.status) {
        console.error('Error status:', error.response.status);
      }
      Alert.alert('Lỗi', 'Không thể tải lên tài liệu. Vui lòng thử lại.');
    },
  });

  return (
    <KeyboardAvoidingView
      style={[styles.container, {backgroundColor: theme.colors.background}]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={FOOTER_HEIGHT + 24}>
      <View style={styles.container}>
        <CCAppBar label="Đính kèm giấy tờ" isBack={true} />
        <View style={styles.stepMenuVertical}>
          <StepMenuVertical currentStep={3} options={optionSteps} />
        </View>
        <View style={styles.contentWithButton}>
          <ScrollView
            contentContainerStyle={styles.scrollContentContainer}
            keyboardShouldPersistTaps="handled">
            {Array.isArray(itemsArray) &&
              itemsArray.map((item: any, idx: number) => {
                if (item.dataType === 'uri') {
                  return (
                    <Card key={item.code || idx} style={styles.card}>
                      <Card.Title title={item.name} />
                      <Card.Actions style={styles.cardActions}>
                        {!item.uri && (
                          <Button
                            mode="contained"
                            style={styles.fullWidthButton}
                            onPress={() => {
                              navHome.navigate('DocumentsScreen', {
                                code: item.code,
                                name: item.name,
                              });
                            }}>
                            Chọn giấy tờ
                          </Button>
                        )}
                        {item.uri && (
                          <>
                            <Button
                              mode="contained"
                              style={styles.fullWidthButton}
                              onPress={() => {
                                nav.navigate('DocumentDetailScreen', {
                                  uri: item.uri,
                                });
                              }}>
                              {item.uriMetaData?.name || 'Xem giấy tờ'}
                            </Button>
                            <IconButton
                              icon="delete"
                              onPress={() => {
                                clearUriAndMetaDataByCode(item.code);
                              }}
                            />
                          </>
                        )}
                      </Card.Actions>
                    </Card>
                  );
                }
                if (item.dataType === 'form') {
                  return (
                    <Card key={item.code || idx} style={styles.card}>
                      <Card.Title title={item.name} />
                      <Card.Actions style={styles.cardActions}>
                        <Button
                          mode="contained"
                          style={styles.fullWidthButton}
                          onPress={() => {
                            nav.navigate('PDFViewerScreen', {
                              pdfPath: item.physicalFilePath,
                            });
                          }}>
                          Biểu mẫu điện tử .pdf
                        </Button>
                      </Card.Actions>
                    </Card>
                  );
                }
                return null;
              })}
          </ScrollView>
          <View style={commonStyle.footer}>
            <Button
              style={commonStyle.footerButton}
              mode="contained"
              onPress={async () => {
                // TODO: Add next step logic here
                try {
                  if (formItem) {
                    const formPDFAsBase64 = await getPdfAsBase64(
                      formItem.physicalFilePath,
                    );
                    await uploadMutation.mutateAsync({
                      docOwnerType: 'Resident',
                      docType: 'Personal',
                      docName: formItem.name,
                      issuedOn: new Date().toISOString(),
                      validFrom: new Date().toISOString(),
                      fileSize: formPDFAsBase64?.length || 0,
                      docFileType: 'pdf',
                      docFileData: formPDFAsBase64,
                      metaData: {},
                    });
                  }

                  const identificationData = {
                    FullName: displayName,
                    Nationality: stepOneFormData?.Nationality.value.value || '',
                    CitizenCode: getCitizenCode(userProfile),
                    Sex: stepOneFormData?.Sex.value.value || '',
                    Nation: stepOneFormData?.Nation.value.value || '',
                    Religion: stepOneFormData?.Religion.value.value || '',
                    DateOfBirth: stepOneFormData?.DateOfBirth
                      ? moment(stepOneFormData.DateOfBirth, 'YYYY-MM-DD')
                          .format('DD/MM/YYYY')
                          .toString() // Format to DD/MM/YYYY
                      : '',
                    DateOfProvision: stepOneFormData?.DateOfProvision
                      ? moment(stepOneFormData.DateOfProvision).format(
                          'DD/MM/YYYY',
                        )
                      : '',
                    PlaceOfProvision:
                      stepOneFormData?.PlaceOfIssues.value.value,
                    PlaceOfOrigin: stepOneFormData?.PlaceOfOrigin || '',
                    PlaceOfResidence: stepOneFormData?.PlaceOfResidence || '',
                  };

                  const name = templateDetail.name;
                  const templateId = templateDetail.id;
                  const type = 'HSMOBILE';
                  let metadata: any = {
                    dvc: {
                      maTTHC: templateDetail.code,
                      maDonVi: forwardData?.CapDonViID,
                    },

                    identification: identificationData,
                  };
                  if (formItem) {
                    metadata = {
                      ...metadata,
                      forms: [
                        {
                          code: formItem.code,
                          keyEformID: currentUuid,
                          data: {
                            ...metadataSubmitFlow,
                            // soLuong: Number(metadataSubmitFlow?.soLuong),
                          },
                        },
                      ],
                    };
                  }

                  const requestBody = {
                    name,
                    templateId,
                    type,
                    data: getSubmitItem(itemsArray),
                    metadata,
                  };

                  let requesterServiceId;
                  try {
                    const requesterServiceByPartnerCodeRs =
                      await dvcQuangNinh.getRequesterServiceByPartnerCode(
                        `${forwardData?.CapDonViID}`,
                      );

                    requesterServiceId =
                      requesterServiceByPartnerCodeRs?.data?.data
                        ?.requesterServiceId;
                  } catch (error) {
                    console.log(
                      'Error fetching requester service by partner code:',
                      error,
                    );
                  }

                  const documentSetCreated = await documentSet.createDocSet(
                    requestBody,
                  );
                  const createdDocumentSetUri = documentSetCreated?.data.uri;
                  try {
                    const docSet = {
                      uri: createdDocumentSetUri,
                      requesterServiceId,
                      identification: identificationData,
                      forwardData: {
                        CapThucHien: forwardData?.CapThucHien,
                        DiaChiNopHoSo: forwardData?.TenDonViThucHien,
                        DonViCapChaId: forwardData?.DonViCapCha,
                        DonViID: forwardData?.DonViThucHien,
                        DonViHCCID: forwardData?.DonViHCCID,
                      },
                    };

                    const documentSetSubmitted = await documentSet.submitDocSet(
                      docSet,
                    );

                    setSubmitDocumentResponse(documentSetSubmitted.data.data);
                    try {
                      const submitDocumentInstantResponse =
                        await dvcQuangNinh.submitDocumentInstant({
                          shareDocumentId:
                            documentSetSubmitted.data.data.shareDocumentId,
                        });
                      setSubmitDocumentInstantResponse(
                        submitDocumentInstantResponse.data.data,
                      );
                      nav.navigate('StepFour');
                    } catch (error) {
                      console.error(
                        'Error submitting document:',
                        JSON.stringify(error),
                      );
                      throw error;
                    }
                  } catch (error) {
                    console.error('Error creating document set:', error);
                    throw error;
                  }
                } catch (error) {
                  console.error(
                    'Error in Step Three:',
                    JSON.stringify(error),
                    error,
                  );
                  Alert.alert(
                    'Lỗi',
                    'Đã xảy ra lỗi trong quá trình xử lý. Vui lòng thử lại.',
                  );
                }
              }}>
              Tiếp theo
            </Button>
          </View>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  scrollContentContainer: {
    paddingBottom: 80,
    flexGrow: 1,
    paddingHorizontal: 8,
    alignItems: 'stretch',
  },
  contentWithButton: {
    flex: 1,
    justifyContent: 'center',
    padding: 16,
    position: 'relative',
  },
  stepMenuVertical: {
    marginTop: 4,
    marginBottom: 4,
  },
  container: {
    flex: 1,
  },
  card: {
    marginBottom: 16,
    elevation: 2,
    width: '100%',
    marginLeft: 0,
    marginRight: 0,
    alignSelf: 'stretch',
  },
  cardActions: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    flexWrap: 'wrap',
    gap: 8,
  },
  fullWidthButton: {
    flex: 1,

    maxWidth: '100%',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  stickyButtonContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    padding: 8,
    zIndex: 10,
  },
});

export default StepThree;
