import React, {
  memo,
  useRef,
  useState,
  useEffect,
  useCallback,
  // useLayoutEffect,
} from 'react';
import {StyleSheet, View, BackHandler} from 'react-native';
import {WebView} from 'react-native-webview';
import {IOptionMenuItem, StepMenuVertical} from '../../components/AcStepMenu';
import {FOOTER_HEIGHT} from '../../styles';
import {Button, useTheme} from 'react-native-paper';
import {CCAppBar} from '../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import {
  useNavigation,
  NavigationProp,
  RouteProp,
  useRoute,
} from '@react-navigation/native';
import {MainStackParamList} from '../../navigation/MainNavigator';
import {useAuthStore} from '@ac-mobile/common';

import {useMutation} from 'react-query';
import RNFS from 'react-native-fs';
import {preprocessHtml} from '../../requester-biz-service/apis/document-sets-api';
import {ENDPOINT} from '../../api/api-config';
import {useSubmitFlow} from '../../hooks/useSubmitFlow';
import {useCommonProcedure} from '../../stores';
import {commonStyle} from './commonStyle';
// import {documentSet} from '../../requester-biz-service/apis/document-sets-api';

const optionSteps: IOptionMenuItem[] = [
  {id: 1, name: 'Khai báo thông tin người yêu cầu'},
  {id: 2, name: 'Kê khai biểu mẫu điện tử'},
  {id: 3, name: 'Đính kèm giấy tờ'},
  {id: 4, name: 'Nhận kết quả & thanh toán'},
];

type StepTwoScreenRouteProp = RouteProp<MainStackParamList, 'StepTwo'>;

export const StepTwo = memo(() => {
  const route = useRoute<StepTwoScreenRouteProp>();
  const theme = useTheme();
  const nav = useNavigation<NavigationProp<MainStackParamList>>();
  const {userFormId} = route.params || {};
  const webViewRef = useRef<WebView>(null);
  const {accessToken} = useAuthStore();
  const [currentMode, setCurrentMode] = useState('edit-mode');
  const [htmlData, setHtmlData] = useState('');
  const [isLoadingHtml, setIsLoadingHtml] = useState(false);
  const [lastPdfPath, setLastPdfPath] = useState<string | null>(null);
  const templateDetail = useCommonProcedure(state => state.templateDetail);
  const setMetadata = useSubmitFlow(state => state.setMetadata);
  const setCurrentUuid = useSubmitFlow(state => state.setCurrentUuid);

  const addItemToArray = useSubmitFlow(state => state.addItemToArray);
  const removeItemFromArray = useSubmitFlow(state => state.removeItemFromArray);
  // Delay function for ms milliseconds
  const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

  // Periodic mode check
  useEffect(() => {
    const interval = setInterval(() => {
      getMode();
    }, 500);
    return () => clearInterval(interval);
  }, []);

  // WebView message handler
  const lastModeRef = useRef();
  const onMessage = (event: {nativeEvent: {data: any}}) => {
    const message = event.nativeEvent.data;
    // console.log('📥 WebView message:', message);
    let mode = null;
    if (message === 'sign-mode' || message === 'sign-mode-loaded') {
      mode = 'sign-mode';
    } else if (message === 'edit-mode') {
      mode = 'edit-mode';
    }
    if (mode && lastModeRef.current !== mode) {
      setCurrentMode(mode);
      lastModeRef.current = mode;
    }
    if (message.startsWith('meta-data')) {
      try {
        const meta = JSON.parse(message.replace('meta-data:', ''));

        setMetadata(meta);
      } catch (e) {}
    } else if (message === 'INPUTS_RENDERED') {
      // Form is ready
    } else if (!mode) {
      console.log('📄 HTML data received: ', message.length);
      setHtmlData(message);
      // setIsLoadingHtml(false);
    }
  };

  useEffect(() => {
    setCurrentUuid(userFormId || null);
  });

  // JS helpers
  const handleEditMode = () => {
    webViewRef.current?.injectJavaScript(
      `window.postMessage("edit-mode", "*");`,
    );
  };
  const handleSignMode = () => {
    webViewRef.current?.injectJavaScript(
      `window.postMessage("sign-mode", "*");`,
    );
  };
  const getMode = () => {
    webViewRef.current?.injectJavaScript(
      `window.postMessage("get-mode", "*");`,
    );
  };
  const getHtml = () => {
    console.log('🔄 Requesting HTML content...');
    webViewRef.current?.injectJavaScript(
      `window.postMessage("get-html", "*");`,
    );
  };
  const requestMetadata = () => {
    console.log('🔄 Requesting metadata...');
    webViewRef.current?.injectJavaScript(
      `window.postMessage("get-meta-data", "*");`,
    );
  };

  // PDF conversion (call backend)
  const pdfMutation = useMutation({
    mutationFn: async () => {
      console.log('[PDF Mutation] Start PDF conversion');
      if (!htmlData) {
        throw new Error('HTML content is empty');
      }
      const cleanHtml = preprocessHtml(htmlData);
      console.log('accessToken:', accessToken);
      const url = `${ENDPOINT}/requester-biz/api/v1/document-sets/method/print/html-to-pdf`;
      console.log('[PDF Mutation] Fetching:', url);
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({html: cleanHtml}),
      });
      console.log('[PDF Mutation] Response status:', response.status);
      if (!response.ok) {
        console.log(
          '[PDF Mutation] Response not OK:',
          response.status,
          response.statusText,
        );
        throw new Error('PDF conversion failed');
      }
      const arrayBuffer = await response.arrayBuffer();

      const uint8Array = new Uint8Array(arrayBuffer);

      let binary = '';
      for (let i = 0; i < uint8Array.length; i++) {
        binary += String.fromCharCode(uint8Array[i]);
      }
      const base64 = btoa(binary);
      console.log('[PDF Mutation] Base64 length:', base64.length);
      return base64;
    },
  });

  const convertHtmlToPdf = useCallback(async () => {
    return pdfMutation.mutateAsync();
  }, [pdfMutation]);

  // Back button handler
  const handleBackPress = useCallback(() => {
    // Always force the system to execute back logic
    if (isLoadingHtml) return true;
    if (currentMode === 'sign-mode') {
      handleEditMode();
      setHtmlData('');
      return true; // Prevent navigation, stay in edit mode
    } else if (currentMode === 'edit-mode') {
      nav.goBack();
      return true; // We handled the navigation
    }
    // Default case: allow system to handle back press
    nav.goBack();
    return true;
  }, [currentMode, isLoadingHtml, nav]);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      handleBackPress,
    );
    return () => backHandler.remove();
  }, [handleBackPress]);

  // Next button handler
  const handleNextPress = async () => {
    getMode();
    if (currentMode === 'edit-mode') {
      handleSignMode();
    } else if (currentMode === 'sign-mode') {
      if (htmlData.length === 0) {
        setIsLoadingHtml(true);
        setHtmlData('');
        requestMetadata();
        getHtml();
        console.log('🔄 Waiting for HTML data...');
        try {
          await delay(1000);
          console.log('HTML data received:', htmlData.length);

          // Check for XPath button and click if it exists
          webViewRef.current?.injectJavaScript(`
            (function() {
              try {
                const button = document.evaluate(
                  "/html/body/div[2]/div[3]/button[2]",
                  document,
                  null,
                  XPathResult.FIRST_ORDERED_NODE_TYPE,
                  null
                ).singleNodeValue;

                if (button) {
                  console.log('XPath button found, clicking...');
                  button.click();
                  return true;
                } else {
                  console.log('XPath button not found');
                  return false;
                }
              } catch (error) {
                console.error('Error checking XPath button:', error);
                return false;
              }
            })();
          `);

          // Wait 500ms before proceeding to the last part
          await delay(1500);

          console.log('🔄 Converting HTML to PDF...');
          const pdfBase64 = await convertHtmlToPdf();
          const pdfPath = `${RNFS.CachesDirectoryPath}/form_${Date.now()}.pdf`;
          // If lastPdfPath exists and is different, delete the old file
          if (lastPdfPath && lastPdfPath !== pdfPath) {
            try {
              const exists = await RNFS.exists(lastPdfPath);
              if (exists) {
                await RNFS.unlink(lastPdfPath);
                console.log('Deleted old PDF:', lastPdfPath);
              }
            } catch (err) {
              console.warn('Failed to delete old PDF:', err);
            }
          }
          await RNFS.writeFile(pdfPath, pdfBase64, 'base64');
          setLastPdfPath(pdfPath);
          setIsLoadingHtml(false);
          console.log('PDF saved to:', pdfPath);
          const formItem = templateDetail.data.find(
            (item: {dataType: string}) => item.dataType === 'form',
          );
          removeItemFromArray(formItem.code);
          if (formItem) {
            addItemToArray({
              name: formItem.name,
              code: formItem.code,
              dataType: formItem.dataType,
              // value: pdfBase64,
              physicalFilePath: pdfPath,
              required: formItem.required || true,
            });
          }
          nav.navigate('StepThree');
          setHtmlData('');
        } catch (e) {
          console.error('PDF conversion error:', e);
          setIsLoadingHtml(false);
        }
      }
    }
  };

  return (
    <View
      style={[styles.container, {backgroundColor: theme.colors.background}]}>
      <CCAppBar
        label={'Kê khai biểu mẫu điện tử'}
        isBack={true}
        onBackPressed={handleBackPress}
      />
      <View style={styles.stepMenuVertical}>
        <StepMenuVertical currentStep={2} options={optionSteps} />
      </View>
      <View style={{flex: 1}}>
        <WebView
          style={[styles.webview, {marginBottom: 50}]}
          ref={webViewRef}
          incognito={true}
          source={{
            uri: `https://tokhaidientu.quangninh.gov.vn/mobile/?viewMode=edit&formUserId=${userFormId}&token=${accessToken}&&requesterServiceId=${'09a2ddfc-f240-4d0a-89fa-2007b90ee551'}`,
          }}
          onLoadEnd={() => {}}
          onMessage={onMessage}
          javaScriptEnabled
          injectedJavaScript={`
            (function() {
              // Inject meta viewport
              var meta = document.createElement('meta');
              meta.setAttribute('name', 'viewport');
              meta.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
              document.getElementsByTagName('head')[0].appendChild(meta);
              // Bridge window.messageHandler.postMessage to window.ReactNativeWebView.postMessage
              window.messageHandler = {
                postMessage: function(msg) {
                  window.ReactNativeWebView.postMessage(msg);
                }
              };
              // If INPUTS_RENDERED is used in Flutter, send it here too
              window.ReactNativeWebView.postMessage('INPUTS_RENDERED');
            })();
          `}
        />
      </View>
      <View style={commonStyle.footer}>
        <Button
          mode="contained"
          style={commonStyle.footerButton}
          loading={isLoadingHtml}
          onPress={handleNextPress}>
          Tiếp theo
        </Button>
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerZIndex: {
    zIndex: 999,
  },
  content: {
    flexGrow: 1,
    paddingBottom: FOOTER_HEIGHT,
    zIndex: 999,
  },
  stepMenuVertical: {
    marginTop: 4,
    marginBottom: 4,
  },
  overlayDialogStart: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 4,
    shadowOffset: {width: 0, height: -2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  header: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  subHeader: {
    textAlign: 'center',
    marginTop: 4,
  },
  webview: {
    paddingBottom: 10,
  },
});

export default StepTwo;
